import React, { useState } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { 
  ArrowDownTrayIcon, 
  DocumentArrowDownIcon, 
  ArrowPathIcon,
  ShareIcon 
} from '@heroicons/react/24/outline';
import { SimulationState, SimulationConfig } from '../types';
import toast from 'react-hot-toast';

interface ResultsAnalysisProps {
  simulationState: SimulationState;
  strategyName: string;
  config: SimulationConfig;
  onStartNew: () => void;
}

export const ResultsAnalysis: React.FC<ResultsAnalysisProps> = ({
  simulationState,
  strategyName,
  config,
  onStartNew
}) => {
  const [exportFormat, setExportFormat] = useState<'csv' | 'pdf'>('csv');

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getReturnColor = (value: number) => {
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getPerformanceGrade = (totalReturn: number) => {
    if (totalReturn >= 20) return { grade: 'A+', color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900/20' };
    if (totalReturn >= 15) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900/20' };
    if (totalReturn >= 10) return { grade: 'B+', color: 'text-blue-600', bg: 'bg-blue-100 dark:bg-blue-900/20' };
    if (totalReturn >= 5) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-100 dark:bg-blue-900/20' };
    if (totalReturn >= 0) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-100 dark:bg-yellow-900/20' };
    return { grade: 'D', color: 'text-red-600', bg: 'bg-red-100 dark:bg-red-900/20' };
  };

  const handleExport = (format: 'csv' | 'pdf') => {
    // Mock export functionality
    toast.success(`Exporting results as ${format.toUpperCase()}...`);
    
    if (format === 'csv') {
      // Create CSV content
      const csvContent = [
        'Metric,Value',
        `Strategy,${strategyName}`,
        `Initial Capital,${config.initial_capital}`,
        `Final Value,${simulationState.portfolio.totalValue}`,
        `Total Return,${simulationState.portfolio.totalReturn}%`,
        `Sharpe Ratio,${simulationState.performance.sharpeRatio}`,
        `Max Drawdown,${simulationState.performance.maxDrawdown}%`,
        `Win Rate,${simulationState.performance.winRate}%`,
        `Total Trades,${simulationState.performance.totalTrades}`,
        `Profit Factor,${simulationState.performance.profitFactor}`
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `trading-simulation-results-${Date.now()}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  const handleShare = () => {
    const shareText = `🚀 Trading Simulation Results\n\nStrategy: ${strategyName}\nReturn: ${formatPercentage(simulationState.portfolio.totalReturn)}\nSharpe Ratio: ${simulationState.performance.sharpeRatio.toFixed(2)}\n\nPowered by Trading Simulator`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Trading Simulation Results',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast.success('Results copied to clipboard!');
    }
  };

  const performanceGrade = getPerformanceGrade(simulationState.portfolio.totalReturn);

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Simulation Results</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Complete analysis of your <strong>{strategyName}</strong> simulation
        </p>
        <div className="flex justify-center mt-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <div className="w-16 h-1 bg-green-500"></div>
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <div className="w-16 h-1 bg-green-500"></div>
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <div className="w-16 h-1 bg-green-500"></div>
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
          </div>
        </div>
      </div>

      {/* Performance Grade */}
      <div className="flex justify-center">
        <div className={`inline-flex items-center px-6 py-3 rounded-lg ${performanceGrade.bg}`}>
          <div className="text-center">
            <div className={`text-3xl font-bold ${performanceGrade.color}`}>
              {performanceGrade.grade}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Performance Grade
            </div>
          </div>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card title="Total Return" className="text-center">
          <div className={`text-3xl font-bold ${getReturnColor(simulationState.portfolio.totalReturn)}`}>
            {formatPercentage(simulationState.portfolio.totalReturn)}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            {formatCurrency(simulationState.portfolio.totalValue - config.initial_capital)} profit
          </div>
        </Card>

        <Card title="Final Portfolio Value" className="text-center">
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(simulationState.portfolio.totalValue)}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            From {formatCurrency(config.initial_capital)}
          </div>
        </Card>

        <Card title="Sharpe Ratio" className="text-center">
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {simulationState.performance.sharpeRatio.toFixed(2)}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            Risk-adjusted return
          </div>
        </Card>

        <Card title="Max Drawdown" className="text-center">
          <div className={`text-3xl font-bold ${getReturnColor(simulationState.performance.maxDrawdown)}`}>
            {formatPercentage(simulationState.performance.maxDrawdown)}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            Worst decline
          </div>
        </Card>
      </div>

      {/* Detailed Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card title="Risk Metrics" subtitle="Risk-adjusted performance analysis">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Volatility:</span>
              <span className="font-medium">{formatPercentage(simulationState.performance.volatility)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Sharpe Ratio:</span>
              <span className="font-medium">{simulationState.performance.sharpeRatio.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Max Drawdown:</span>
              <span className={`font-medium ${getReturnColor(simulationState.performance.maxDrawdown)}`}>
                {formatPercentage(simulationState.performance.maxDrawdown)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Calmar Ratio:</span>
              <span className="font-medium">
                {(simulationState.portfolio.totalReturn / Math.abs(simulationState.performance.maxDrawdown)).toFixed(2)}
              </span>
            </div>
          </div>
        </Card>

        <Card title="Trading Statistics" subtitle="Trade execution and success metrics">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Total Trades:</span>
              <span className="font-medium">{simulationState.performance.totalTrades}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Win Rate:</span>
              <span className="font-medium">{formatPercentage(simulationState.performance.winRate)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Profit Factor:</span>
              <span className="font-medium">{simulationState.performance.profitFactor.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Avg Trade Return:</span>
              <span className={`font-medium ${getReturnColor(simulationState.performance.avgTradeReturn)}`}>
                {formatPercentage(simulationState.performance.avgTradeReturn)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Best Trade:</span>
              <span className="font-medium text-green-600">
                {formatPercentage(simulationState.performance.bestTrade)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Worst Trade:</span>
              <span className="font-medium text-red-600">
                {formatPercentage(simulationState.performance.worstTrade)}
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Strategy Configuration Summary */}
      <Card title="Strategy Configuration" subtitle="Parameters used in this simulation">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 dark:text-white">Strategy Details</h4>
            <div className="text-sm space-y-1">
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Strategy:</span> <span className="text-gray-900 dark:text-white">{strategyName}</span></p>
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Initial Capital:</span> <span className="text-gray-900 dark:text-white">{formatCurrency(config.initial_capital)}</span></p>
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Position Size:</span> <span className="text-gray-900 dark:text-white">{formatPercentage(config.position_size)}</span></p>
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Max Positions:</span> <span className="text-gray-900 dark:text-white">{config.max_positions}</span></p>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 dark:text-white">Trading Costs</h4>
            <div className="text-sm space-y-1">
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Commission:</span> <span className="text-gray-900 dark:text-white">{formatPercentage(config.commission_rate)}</span></p>
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Slippage:</span> <span className="text-gray-900 dark:text-white">{formatPercentage(config.slippage_rate)}</span></p>
              <p><span className="text-gray-700 dark:text-gray-300 font-medium">Speed:</span> <span className="text-gray-900 dark:text-white">{config.speed_multiplier}x</span></p>
            </div>
          </div>
        </div>
      </Card>

      {/* Trade History */}
      {simulationState.trades && simulationState.trades.length > 0 && (
        <Card title="Trade History" subtitle={`${simulationState.trades.length} trades executed`}>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Side</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Quantity</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Value</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">P&L</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {simulationState.trades.slice(-10).reverse().map((trade, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {new Date(trade.timestamp).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        trade.side === 'BUY' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {trade.side}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatCurrency(trade.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {trade.quantity.toFixed(6)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatCurrency(trade.price * trade.quantity)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {trade.pnl !== undefined && (
                        <span className={getReturnColor(trade.pnl)}>
                          {formatCurrency(trade.pnl)}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex space-x-2">
          <Button
            onClick={() => handleExport('csv')}
            variant="secondary"
            icon={<ArrowDownTrayIcon />}
          >
            Export CSV
          </Button>
          <Button
            onClick={() => handleExport('pdf')}
            variant="secondary"
            icon={<DocumentArrowDownIcon />}
          >
            Export PDF
          </Button>
          <Button
            onClick={handleShare}
            variant="secondary"
            icon={<ShareIcon />}
          >
            Share Results
          </Button>
        </div>
        <Button
          onClick={onStartNew}
          icon={<ArrowPathIcon />}
          size="lg"
          className="bg-primary-600 hover:bg-primary-700"
        >
          Start New Simulation
        </Button>
      </div>
    </div>
  );
};
