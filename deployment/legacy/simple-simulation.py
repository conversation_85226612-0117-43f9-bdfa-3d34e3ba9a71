import json
import uuid
import random
import time

# Global storage for simulations
simulations = {}

def lambda_handler(event, context):
    try:
        http_method = event.get('httpMethod', 'GET')
        path = event.get('path', '')
        path_params = event.get('pathParameters') or {}
        
        # CORS headers
        headers = {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Allow-Methods': '*'
        }
        
        # Create simulation
        if http_method == 'POST' and path == '/api/simulations':
            simulation_id = str(uuid.uuid4())
            body = json.loads(event.get('body', '{}'))
            config = body.get('config', {})
            strategy_id = body.get('strategy_id', 'ma_crossover')
            
            # Store simulation
            simulations[simulation_id] = {
                'id': simulation_id,
                'status': 'created',
                'strategy_id': strategy_id,
                'config': config,
                'start_date': config.get('start_date', '2024-01-01'),
                'end_date': config.get('end_date', '2024-01-31'),
                'current_date': config.get('start_date', '2024-01-01'),
                'progress': 0.0,
                'created_at': time.time(),
                'portfolio': {
                    'totalValue': config.get('initial_capital', 100000),
                    'cash': config.get('initial_capital', 100000) * 0.2,
                    'positions': [],
                    'dailyReturn': 0.0,
                    'totalReturn': 0.0
                },
                'trades': [],
                'performance': {
                    'sharpeRatio': 0.0,
                    'maxDrawdown': 0.0,
                    'volatility': 0.0,
                    'winRate': 0.0,
                    'profitFactor': 1.0,
                    'totalTrades': 0,
                    'avgTradeReturn': 0.0,
                    'bestTrade': 0.0,
                    'worstTrade': 0.0
                }
            }
            
            return {
                'statusCode': 201,
                'headers': headers,
                'body': json.dumps({
                    'success': True,
                    'data': {
                        'simulation_id': simulation_id,
                        'status': 'created',
                        'message': 'Simulation created successfully'
                    }
                })
            }
        
        # Start simulation
        elif http_method == 'POST' and 'simulationId' in path_params and path.endswith('/start'):
            sim_id = path_params['simulationId']
            if sim_id in simulations:
                simulations[sim_id]['status'] = 'running'
                simulations[sim_id]['started_at'] = time.time()
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'success': True,
                    'data': {
                        'simulation_id': sim_id,
                        'status': 'running',
                        'message': 'Simulation started'
                    }
                })
            }
        
        # Get simulation state
        elif http_method == 'GET' and 'simulationId' in path_params and path.endswith('/state'):
            sim_id = path_params['simulationId']
            
            if sim_id not in simulations:
                return {
                    'statusCode': 404,
                    'headers': headers,
                    'body': json.dumps({
                        'success': False,
                        'error': 'Simulation not found'
                    })
                }
            
            sim = simulations[sim_id]
            
            # Update simulation progress if running
            if sim['status'] == 'running' and 'started_at' in sim:
                elapsed_time = time.time() - sim['started_at']
                speed_multiplier = sim['config'].get('speed_multiplier', 1.0)
                
                # Complete simulation in 30 seconds at 1x speed
                simulation_duration = 30.0 / speed_multiplier
                progress = min(elapsed_time / simulation_duration, 1.0)
                sim['progress'] = progress
                
                # Update current date based on progress
                if progress >= 1.0:
                    sim['status'] = 'completed'
                    sim['current_date'] = sim['end_date']
                else:
                    # Simple date progression
                    start_parts = sim['start_date'].split('-')
                    start_day = int(start_parts[2])
                    days_to_add = int(progress * 30)  # Assume 30 days max
                    current_day = min(start_day + days_to_add, 31)
                    sim['current_date'] = f"{start_parts[0]}-{start_parts[1]}-{current_day:02d}"
                
                # Update portfolio based on progress
                initial_capital = sim['config'].get('initial_capital', 100000)
                strategy_performance = {
                    'ma_crossover': 0.15,
                    'rsi_mean_reversion': 0.12,
                    'momentum': 0.18,
                    'balanced_ensemble': 0.14,
                    'confidence_weighted_ensemble': 0.16,
                    'adaptive_ensemble': 0.15
                }.get(sim['strategy_id'], 0.15)
                
                # Add volatility
                volatility = random.uniform(-0.05, 0.05)
                total_return = (strategy_performance + volatility) * progress
                
                sim['portfolio']['totalValue'] = initial_capital * (1 + total_return)
                sim['portfolio']['totalReturn'] = total_return * 100
                sim['portfolio']['dailyReturn'] = random.uniform(-3, 5)
                sim['portfolio']['cash'] = sim['portfolio']['totalValue'] * random.uniform(0.1, 0.3)
                
                # Update performance metrics
                sim['performance']['totalTrades'] = max(1, int(progress * 25))
                sim['performance']['winRate'] = random.uniform(45, 75)
                sim['performance']['sharpeRatio'] = random.uniform(0.5, 2.2)
                sim['performance']['maxDrawdown'] = random.uniform(-20, -2)
                sim['performance']['volatility'] = random.uniform(15, 35)
                sim['performance']['profitFactor'] = random.uniform(1.0, 2.5)
                sim['performance']['avgTradeReturn'] = total_return * 100 / max(sim['performance']['totalTrades'], 1)
                sim['performance']['bestTrade'] = random.uniform(5, 15)
                sim['performance']['worstTrade'] = random.uniform(-12, -2)
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'success': True,
                    'data': sim
                })
            }
        
        # Stop simulation
        elif http_method == 'POST' and 'simulationId' in path_params and path.endswith('/stop'):
            sim_id = path_params['simulationId']
            if sim_id in simulations:
                simulations[sim_id]['status'] = 'stopped'
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'success': True,
                    'data': {
                        'simulation_id': sim_id,
                        'status': 'stopped',
                        'message': 'Simulation stopped'
                    }
                })
            }
        
        # Default response
        return {
            'statusCode': 404,
            'headers': headers,
            'body': json.dumps({
                'success': False,
                'error': 'Endpoint not found'
            })
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': '*',
                'Access-Control-Allow-Methods': '*'
            },
            'body': json.dumps({
                'success': False,
                'error': f'Internal server error: {str(e)}'
            })
        }
