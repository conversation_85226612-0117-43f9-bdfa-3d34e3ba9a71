import React, { useState, useEffect } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { CandlestickChart } from '../components/charts/CandlestickChart';
import { StopIcon, PauseIcon, PlayIcon } from '@heroicons/react/24/outline';
import { SimulationState, SimulationConfig, CandlestickData, MarketData } from '../types';
import { apiService } from '../services/api';
import toast from 'react-hot-toast';

interface LiveSimulationProps {
  simulationId: string;
  strategyName: string;
  config: SimulationConfig;
  onComplete: (finalState: SimulationState) => void;
  onStop: () => void;
}

export const LiveSimulation: React.FC<LiveSimulationProps> = ({
  simulationId,
  strategyName,
  config,
  onComplete,
  onStop
}) => {
  const [simulationState, setSimulationState] = useState<SimulationState | null>(null);
  const [isRunning, setIsRunning] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const [marketData, setMarketData] = useState<CandlestickData[]>([]);
  const [loadingMarketData, setLoadingMarketData] = useState(true);

  // Fetch market data for the simulation period
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoadingMarketData(true);
        const symbol = 'BTC-USD'; // Default symbol

        // Use config dates if available, otherwise use recent data
        const params = config.use_custom_date_range && config.start_date && config.end_date
          ? { start_date: config.start_date, end_date: config.end_date }
          : { days: 30 };

        const response = await apiService.getMarketData(symbol, params);
        if (response.success && response.data?.data) {
          setMarketData(response.data.data);
        }
      } catch (error) {
        console.error('Failed to fetch market data:', error);
        toast.error('Failed to load market data');
      } finally {
        setLoadingMarketData(false);
      }
    };

    fetchMarketData();
  }, [config]);

  // Poll simulation state
  useEffect(() => {
    if (!isRunning || isPaused) return;

    const pollInterval = setInterval(async () => {
      try {
        const response = await apiService.getSimulationState(simulationId);
        if (response.success && response.data) {
          setSimulationState(response.data);

          // Update market data if included in simulation state
          if (response.data.chart_data && response.data.chart_data.length > 0) {
            setMarketData(response.data.chart_data);
            setLoadingMarketData(false);
          }

          // Check if simulation is complete
          if (response.data.status === 'completed') {
            setIsRunning(false);
            clearInterval(pollInterval);
            setTimeout(() => onComplete(response.data!), 2000); // Auto-advance after 2 seconds
          } else if (response.data.status === 'error') {
            setIsRunning(false);
            clearInterval(pollInterval);
            toast.error(response.data.error_message || 'Simulation failed');
          }
        }
      } catch (error) {
        console.error('Failed to fetch simulation state:', error);
      }
    }, 1000); // Poll every second

    return () => clearInterval(pollInterval);
  }, [simulationId, isRunning, isPaused, onComplete]);

  const handleStop = async () => {
    try {
      await apiService.stopSimulation(simulationId);
      setIsRunning(false);
      toast.success('Simulation stopped');
      onStop();
    } catch (error) {
      toast.error('Failed to stop simulation');
    }
  };

  const handlePause = () => {
    setIsPaused(!isPaused);
    toast.success(isPaused ? 'Simulation resumed' : 'Simulation paused');
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getProgressColor = () => {
    if (!simulationState) return 'bg-gray-300';
    if (simulationState.status === 'completed') return 'bg-green-500';
    if (simulationState.status === 'error') return 'bg-red-500';
    return 'bg-primary-500';
  };

  const getReturnColor = (value: number) => {
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Live Simulation</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Running <strong>{strategyName}</strong> with {formatCurrency(config.initial_capital)} initial capital
        </p>
        <div className="flex justify-center mt-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <div className="w-16 h-1 bg-green-500"></div>
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <div className="w-16 h-1 bg-green-500"></div>
            <div className="w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
            <div className="w-16 h-1 bg-gray-300 dark:bg-gray-600"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">4</div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <Card title="Simulation Progress">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {simulationState?.status === 'completed' ? 'Completed' : 
               simulationState?.status === 'error' ? 'Error' :
               isPaused ? 'Paused' : 'Running...'}
            </span>
            <span className="text-sm text-gray-500">
              {simulationState ? `${(simulationState.progress * 100).toFixed(1)}%` : '0%'}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-500 ${getProgressColor()}`}
              style={{ width: `${simulationState ? simulationState.progress * 100 : 0}%` }}
            ></div>
          </div>
          {simulationState?.current_date && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Current Date: {new Date(simulationState.current_date).toLocaleDateString()}
            </p>
          )}
        </div>
      </Card>

      {/* Market Data Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <CandlestickChart
            data={marketData}
            trades={simulationState?.trades || []}
            loading={loadingMarketData}
            currentDate={simulationState?.current_date}
            className="h-96"
          />
        </div>

        {/* Current Market Info */}
        <Card title="Market Information">
          <div className="space-y-4">
            {marketData.length > 0 && (
              <>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Current Price</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    ${marketData[marketData.length - 1]?.close.toFixed(2)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Date Range</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {new Date(marketData[0]?.timestamp).toLocaleDateString()} - {' '}
                    {new Date(marketData[marketData.length - 1]?.timestamp).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Data Points</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {marketData.length} days of historical data
                  </p>
                </div>
                {simulationState?.current_date && (
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Simulation Date</p>
                    <p className="text-sm font-medium text-primary-600 dark:text-primary-400">
                      {new Date(simulationState.current_date).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </>
            )}

            {/* Trading Activity Summary */}
            {simulationState?.trades && simulationState.trades.length > 0 && (
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Trading Activity</p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Buy Orders:</span>
                    <span className="text-green-600 font-medium">
                      {simulationState.trades.filter(t => t.side === 'BUY').length}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Sell Orders:</span>
                    <span className="text-red-600 font-medium">
                      {simulationState.trades.filter(t => t.side === 'SELL').length}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Portfolio Overview */}
      {simulationState?.portfolio && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card title="Portfolio Value">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(simulationState.portfolio.totalValue)}
              </p>
              <p className={`text-sm ${getReturnColor(simulationState.portfolio.totalReturn)}`}>
                {formatPercentage(simulationState.portfolio.totalReturn)}
              </p>
            </div>
          </Card>

          <Card title="Daily Return">
            <div className="text-center">
              <p className={`text-2xl font-bold ${getReturnColor(simulationState.portfolio.dailyReturn)}`}>
                {formatPercentage(simulationState.portfolio.dailyReturn)}
              </p>
              <p className="text-sm text-gray-500">Today's Performance</p>
            </div>
          </Card>

          <Card title="Cash Available">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(simulationState.portfolio.cash)}
              </p>
              <p className="text-sm text-gray-500">
                {((simulationState.portfolio.cash / simulationState.portfolio.totalValue) * 100).toFixed(1)}% of portfolio
              </p>
            </div>
          </Card>

          <Card title="Positions">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {simulationState.portfolio.positions?.length || 0}
              </p>
              <p className="text-sm text-gray-500">Active Positions</p>
            </div>
          </Card>
        </div>
      )}

      {/* Performance Metrics */}
      {simulationState?.performance && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card title="Risk Metrics">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Sharpe Ratio:</span>
                <span className="font-medium">{simulationState.performance.sharpeRatio.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Max Drawdown:</span>
                <span className={`font-medium ${getReturnColor(simulationState.performance.maxDrawdown)}`}>
                  {formatPercentage(simulationState.performance.maxDrawdown)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Volatility:</span>
                <span className="font-medium">{formatPercentage(simulationState.performance.volatility)}</span>
              </div>
            </div>
          </Card>

          <Card title="Trading Statistics">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total Trades:</span>
                <span className="font-medium">{simulationState.performance.totalTrades}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Win Rate:</span>
                <span className="font-medium">{formatPercentage(simulationState.performance.winRate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Profit Factor:</span>
                <span className="font-medium">{simulationState.performance.profitFactor.toFixed(2)}</span>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Recent Trades */}
      {simulationState?.trades && simulationState.trades.length > 0 && (
        <Card title="Recent Trades" subtitle={`${simulationState.trades.length} trades executed`}>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Side
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    P&L
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {simulationState.trades.slice(-5).reverse().map((trade, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {new Date(trade.timestamp).toLocaleTimeString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        trade.side === 'BUY' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {trade.side}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatCurrency(trade.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {trade.quantity.toFixed(6)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {trade.pnl !== undefined && (
                        <span className={getReturnColor(trade.pnl)}>
                          {formatCurrency(trade.pnl)}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {/* Control Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={handlePause}
          variant="secondary"
          icon={isPaused ? <PlayIcon /> : <PauseIcon />}
          disabled={!isRunning || simulationState?.status === 'completed'}
        >
          {isPaused ? 'Resume' : 'Pause'}
        </Button>
        <Button
          onClick={handleStop}
          variant="danger"
          icon={<StopIcon />}
          disabled={!isRunning || simulationState?.status === 'completed'}
        >
          Stop Simulation
        </Button>
      </div>

      {/* Auto-advance message */}
      {simulationState?.status === 'completed' && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded-lg">
            <span className="text-sm font-medium">
              ✅ Simulation completed! Advancing to results in a moment...
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
