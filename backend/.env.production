# Production Environment Configuration
ENVIRONMENT=production
DEBUG=false

# API Configuration
HOST=0.0.0.0
PORT=8000
API_PREFIX=/api

# CORS Configuration
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_ALLOW_CREDENTIALS=true

# Database Configuration (if needed in future)
# DATABASE_URL=postgresql://user:password@host:port/database

# External APIs
YAHOO_FINANCE_TIMEOUT=30
OPENBB_TIMEOUT=30

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security
SECRET_KEY=your-secret-key-here-change-in-production
JWT_SECRET=your-jwt-secret-here-change-in-production

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring and Health Checks
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true

# Performance
WORKERS=4
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
KEEPALIVE=2
