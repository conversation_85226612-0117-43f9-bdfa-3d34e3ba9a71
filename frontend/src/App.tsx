import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { ThemeProvider } from './contexts/ThemeContext';
import { StrategyConfiguration } from './pages/StrategyConfiguration';
import { SimulationSetup } from './pages/SimulationSetup';
import { LiveSimulation } from './pages/LiveSimulation';
import { ResultsAnalysis } from './pages/ResultsAnalysis';
import { StrategyParams, SimulationConfig, SimulationState } from './types';
import { apiService } from './services/api';
import toast from 'react-hot-toast';
import './index.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

type AppStep = 'strategy' | 'setup' | 'simulation' | 'results';

interface AppState {
  step: AppStep;
  strategyId: string;
  strategyName: string;
  strategyParams: StrategyParams;
  simulationConfig: SimulationConfig;
  simulationId: string;
  simulationState: SimulationState | null;
}

function App() {
  const [appState, setAppState] = useState<AppState>({
    step: 'strategy',
    strategyId: '',
    strategyName: '',
    strategyParams: {} as StrategyParams,
    simulationConfig: {
      initial_capital: 100000,
      position_size: 0.1,
      commission_rate: 0.001,
      slippage_rate: 0.0005,
      max_positions: 5,
      speed_multiplier: 1
    },
    simulationId: '',
    simulationState: null
  });

  const handleStrategyNext = (strategyId: string, parameters: StrategyParams) => {
    const strategyNames: { [key: string]: string } = {
      'ma_crossover': 'Moving Average Crossover',
      'rsi_mean_reversion': 'RSI Mean Reversion',
      'momentum': 'Momentum Strategy',
      'balanced_ensemble': 'Balanced Ensemble',
      'confidence_weighted_ensemble': 'Confidence Weighted Ensemble',
      'adaptive_ensemble': 'Adaptive Ensemble'
    };

    setAppState(prev => ({
      ...prev,
      step: 'setup',
      strategyId,
      strategyName: strategyNames[strategyId] || strategyId,
      strategyParams: parameters
    }));
  };

  const handleSetupBack = () => {
    setAppState(prev => ({ ...prev, step: 'strategy' }));
  };

  const handleStartSimulation = async (config: SimulationConfig) => {
    try {
      const createResponse = await apiService.createSimulation(
        appState.strategyId,
        appState.strategyParams,
        config
      );

      if (createResponse.success && createResponse.data) {
        const simulationId = createResponse.data.simulation_id;
        await apiService.startSimulation(simulationId);

        setAppState(prev => ({
          ...prev,
          step: 'simulation',
          simulationConfig: config,
          simulationId
        }));

        toast.success('Simulation started successfully!');
      } else {
        toast.error('Failed to create simulation');
      }
    } catch (error) {
      console.error('Failed to start simulation:', error);
      toast.error('Failed to start simulation');
    }
  };

  const handleSimulationComplete = (finalState: SimulationState) => {
    setAppState(prev => ({
      ...prev,
      step: 'results',
      simulationState: finalState
    }));
  };

  const handleSimulationStop = () => {
    setAppState(prev => ({ ...prev, step: 'setup' }));
  };

  const handleStartNew = () => {
    setAppState({
      step: 'strategy',
      strategyId: '',
      strategyName: '',
      strategyParams: {} as StrategyParams,
      simulationConfig: {
        initial_capital: 100000,
        position_size: 0.1,
        commission_rate: 0.001,
        slippage_rate: 0.0005,
        max_positions: 5,
        speed_multiplier: 1
      },
      simulationId: '',
      simulationState: null
    });
  };

  const renderCurrentStep = () => {
    switch (appState.step) {
      case 'strategy':
        return <StrategyConfiguration onNext={handleStrategyNext} />;

      case 'setup':
        return (
          <SimulationSetup
            strategyName={appState.strategyName}
            onBack={handleSetupBack}
            onStartSimulation={handleStartSimulation}
          />
        );

      case 'simulation':
        return (
          <LiveSimulation
            simulationId={appState.simulationId}
            strategyName={appState.strategyName}
            config={appState.simulationConfig}
            onComplete={handleSimulationComplete}
            onStop={handleSimulationStop}
          />
        );

      case 'results':
        return (
          <ResultsAnalysis
            simulationState={appState.simulationState!}
            strategyName={appState.strategyName}
            config={appState.simulationConfig}
            onStartNew={handleStartNew}
          />
        );

      default:
        return <StrategyConfiguration onNext={handleStrategyNext} />;
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
          <div className="py-8 px-4 sm:px-6 lg:px-8">
            {renderCurrentStep()}
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              className: 'dark:bg-gray-800 dark:text-white',
            }}
          />
        </div>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
