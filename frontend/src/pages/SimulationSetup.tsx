import React, { useState } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input, Slider } from '../components/ui/Input';
import { Select } from '../components/ui/Select';
import { InfoTooltip } from '../components/ui/Tooltip';
import { ArrowLeftIcon, PlayIcon } from '@heroicons/react/24/outline';
import { SimulationConfig } from '../types';
import toast from 'react-hot-toast';

interface SimulationSetupProps {
  strategyName: string;
  onBack: () => void;
  onStartSimulation: (config: SimulationConfig) => void;
}

export const SimulationSetup: React.FC<SimulationSetupProps> = ({
  strategyName,
  onBack,
  onStartSimulation
}) => {
  const [config, setConfig] = useState<SimulationConfig>({
    initial_capital: 100000,
    position_size: 0.1,
    commission_rate: 0.001,
    slippage_rate: 0.0005,
    max_positions: 5,
    speed_multiplier: 1
  });

  const [timeframe, setTimeframe] = useState('30');
  const [timeframeUnit, setTimeframeUnit] = useState('days');
  const [useCustomDateRange, setUseCustomDateRange] = useState(false);
  const [startDate, setStartDate] = useState('2020-01-01');
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);

  const handleStartSimulation = () => {
    if (config.initial_capital < 1000) {
      toast.error('Initial capital must be at least $1,000');
      return;
    }
    if (config.position_size <= 0 || config.position_size > 1) {
      toast.error('Position size must be between 0 and 1');
      return;
    }

    const finalConfig = {
      ...config,
      timeframe_days: timeframeUnit === 'days' ? Number(timeframe) : Number(timeframe) * 30,
      use_custom_date_range: useCustomDateRange,
      start_date: useCustomDateRange ? startDate : undefined,
      end_date: useCustomDateRange ? endDate : undefined
    };

    onStartSimulation(finalConfig);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Simulation Setup</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Configure your simulation parameters for <strong>{strategyName}</strong>
        </p>
        <div className="flex justify-center mt-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <div className="w-16 h-1 bg-green-500"></div>
            <div className="w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
            <div className="w-16 h-1 bg-gray-300 dark:bg-gray-600"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
            <div className="w-16 h-1 bg-gray-300 dark:bg-gray-600"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">4</div>
          </div>
        </div>
      </div>

      {/* Capital Configuration */}
      <Card title="Capital Configuration" subtitle="Set your initial investment and position sizing">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Input
              label={
                <div className="flex items-center space-x-1">
                  <span>Initial Capital</span>
                  <InfoTooltip
                    title="Initial Capital"
                    description="The starting amount of money for your simulation. This represents your total investment capital."
                  />
                </div>
              }
              type="number"
              value={config.initial_capital}
              onChange={(e) => setConfig({...config, initial_capital: Number(e.target.value)})}
              min={1000}
              max={10000000}
              step={1000}
              fullWidth
            />
            <p className="text-sm text-gray-500 mt-1">
              Current: {formatCurrency(config.initial_capital)}
            </p>
          </div>

          <div>
            <Slider
              label={
                <div className="flex items-center space-x-1">
                  <span>Position Size</span>
                  <InfoTooltip
                    title="Position Size"
                    description="The percentage of your capital to use for each trade. Lower values are more conservative."
                  />
                </div>
              }
              value={config.position_size}
              onChange={(value) => setConfig({...config, position_size: value})}
              min={0.01}
              max={1.0}
              step={0.01}
              formatValue={formatPercentage}
            />
            <p className="text-sm text-gray-500 mt-1">
              Max per trade: {formatCurrency(config.initial_capital * config.position_size)}
            </p>
          </div>
        </div>
      </Card>

      {/* Timeframe Configuration */}
      <Card title="Simulation Timeframe" subtitle="Choose simulation duration and historical data range">
        <div className="space-y-6">
          {/* Duration Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Duration"
              type="number"
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              min={1}
              max={365}
              fullWidth
            />
            <Select
              label="Time Unit"
              value={timeframeUnit}
              onChange={setTimeframeUnit}
              options={[
                { value: 'days', label: 'Days' },
                { value: 'months', label: 'Months' }
              ]}
              fullWidth
            />
          </div>

          {/* Historical Data Range Selection */}
          <div className="border-t pt-6">
            <div className="flex items-center space-x-3 mb-4">
              <input
                type="checkbox"
                id="customDateRange"
                checked={useCustomDateRange}
                onChange={(e) => setUseCustomDateRange(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="customDateRange" className="text-sm font-medium text-gray-900 dark:text-white">
                Use Custom Historical Data Range
              </label>
            </div>

            {useCustomDateRange && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Start Date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  min="2010-01-01"
                  max={endDate}
                  fullWidth
                />
                <Input
                  label="End Date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  min={startDate}
                  max={new Date().toISOString().split('T')[0]}
                  fullWidth
                />
              </div>
            )}
          </div>

          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Simulation Duration:</strong> {timeframe} {timeframeUnit}
              {timeframeUnit === 'months' && ` (${Number(timeframe) * 30} days)`}
            </p>
            {useCustomDateRange && (
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                <strong>Historical Data:</strong> {startDate} to {endDate}
                {' '}({Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24))} days of market data)
              </p>
            )}
            {!useCustomDateRange && (
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                <strong>Historical Data:</strong> Most recent {timeframeUnit === 'days' ? timeframe : Number(timeframe) * 30} days
              </p>
            )}
          </div>
        </div>
      </Card>

      {/* Risk Management */}
      <Card title="Risk Management" subtitle="Configure trading costs and risk parameters">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Slider
            label={
              <div className="flex items-center space-x-1">
                <span>Commission Rate</span>
                <InfoTooltip
                  title="Commission Rate"
                  description="The percentage fee charged per trade. Typical values range from 0.05% to 0.25%."
                />
              </div>
            }
            value={config.commission_rate}
            onChange={(value) => setConfig({...config, commission_rate: value})}
            min={0}
            max={0.01}
            step={0.0001}
            formatValue={formatPercentage}
          />

          <Slider
            label={
              <div className="flex items-center space-x-1">
                <span>Slippage Rate</span>
                <InfoTooltip
                  title="Slippage Rate"
                  description="The difference between expected and actual execution price. Accounts for market impact."
                />
              </div>
            }
            value={config.slippage_rate}
            onChange={(value) => setConfig({...config, slippage_rate: value})}
            min={0}
            max={0.005}
            step={0.0001}
            formatValue={formatPercentage}
          />

          <Input
            label={
              <div className="flex items-center space-x-1">
                <span>Max Positions</span>
                <InfoTooltip
                  title="Maximum Positions"
                  description="The maximum number of simultaneous positions allowed. Helps manage risk exposure."
                />
              </div>
            }
            type="number"
            value={config.max_positions}
            onChange={(e) => setConfig({...config, max_positions: Number(e.target.value)})}
            min={1}
            max={20}
            fullWidth
          />
        </div>
      </Card>

      {/* Simulation Speed */}
      <Card title="Simulation Speed" subtitle="Control how fast the simulation runs">
        <div className="max-w-md">
          <Slider
            label={
              <div className="flex items-center space-x-1">
                <span>Speed Multiplier</span>
                <InfoTooltip
                  title="Speed Multiplier"
                  description="Controls how fast the simulation runs. Higher values complete faster but may be harder to follow."
                />
              </div>
            }
            value={config.speed_multiplier}
            onChange={(value) => setConfig({...config, speed_multiplier: value})}
            min={0.5}
            max={10}
            step={0.5}
            formatValue={(v) => `${v}x`}
          />
          <p className="text-sm text-gray-500 mt-2">
            {config.speed_multiplier === 1 ? 'Normal speed' : 
             config.speed_multiplier < 1 ? 'Slower than real-time' : 
             'Faster than real-time'}
          </p>
        </div>
      </Card>

      {/* Summary */}
      <Card title="Configuration Summary" className="border-primary-200 bg-primary-50 dark:bg-primary-900/20">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Strategy:</strong> <span className="text-gray-800 dark:text-gray-200">{strategyName}</span></p>
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Initial Capital:</strong> <span className="text-gray-800 dark:text-gray-200">{formatCurrency(config.initial_capital)}</span></p>
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Position Size:</strong> <span className="text-gray-800 dark:text-gray-200">{formatPercentage(config.position_size)}</span></p>
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Max per Trade:</strong> <span className="text-gray-800 dark:text-gray-200">{formatCurrency(config.initial_capital * config.position_size)}</span></p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Duration:</strong> <span className="text-gray-800 dark:text-gray-200">{timeframe} {timeframeUnit}</span></p>
            {useCustomDateRange && (
              <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Data Range:</strong> <span className="text-gray-800 dark:text-gray-200">{startDate} to {endDate}</span></p>
            )}
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Commission:</strong> <span className="text-gray-800 dark:text-gray-200">{formatPercentage(config.commission_rate)}</span></p>
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Slippage:</strong> <span className="text-gray-800 dark:text-gray-200">{formatPercentage(config.slippage_rate)}</span></p>
            <p className="text-gray-900 dark:text-white"><strong className="text-gray-900 dark:text-white">Max Positions:</strong> <span className="text-gray-800 dark:text-gray-200">{config.max_positions}</span></p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          onClick={onBack}
          variant="secondary"
          icon={<ArrowLeftIcon />}
        >
          Back to Strategy
        </Button>
        <Button
          onClick={handleStartSimulation}
          icon={<PlayIcon />}
          size="lg"
          className="bg-green-600 hover:bg-green-700"
        >
          Start Simulation
        </Button>
      </div>
    </div>
  );
};
