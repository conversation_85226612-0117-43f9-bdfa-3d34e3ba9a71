AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'Trading Simulator - Simplified'

Resources:
  # API Gateway
  TradingApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: prod
      Cors:
        AllowMethods: "'*'"
        AllowHeaders: "'*'"
        AllowOrigin: "'*'"

  # Simple Health Function
  HealthFunction:
    Type: AWS::Serverless::Function
    Properties:
      InlineCode: |
        import json
        def lambda_handler(event, context):
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': '*',
                    'Access-Control-Allow-Methods': '*'
                },
                'body': json.dumps({
                    'success': True,
                    'message': 'Trading Simulator API is healthy',
                    'timestamp': '2025-01-15T10:00:00Z'
                })
            }
      Handler: index.lambda_handler
      Runtime: python3.9
      Events:
        HealthApi:
          Type: Api
          Properties:
            RestApiId: !Ref TradingApi
            Path: /api/health
            Method: get

  # Simple Strategies Function
  StrategiesFunction:
    Type: AWS::Serverless::Function
    Properties:
      InlineCode: |
        import json
        def lambda_handler(event, context):
            strategies = [
                {"id": "ma_crossover", "name": "Moving Average Crossover", "description": "Classic trend-following strategy"},
                {"id": "rsi_mean_reversion", "name": "RSI Mean Reversion", "description": "Contrarian strategy based on RSI"},
                {"id": "momentum", "name": "Momentum Strategy", "description": "Trend-following based on momentum"},
                {"id": "balanced_ensemble", "name": "Balanced Ensemble", "description": "Combines multiple strategies"},
                {"id": "confidence_weighted_ensemble", "name": "Confidence Weighted Ensemble", "description": "Ensemble with confidence weighting"},
                {"id": "adaptive_ensemble", "name": "Adaptive Ensemble", "description": "Dynamic ensemble strategy"}
            ]
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': '*',
                    'Access-Control-Allow-Methods': '*'
                },
                'body': json.dumps({
                    'success': True,
                    'data': strategies
                })
            }
      Handler: index.lambda_handler
      Runtime: python3.9
      Events:
        StrategiesApi:
          Type: Api
          Properties:
            RestApiId: !Ref TradingApi
            Path: /api/strategies
            Method: get

  # S3 Bucket for Frontend
  FrontendBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'trading-frontend-${AWS::AccountId}'
      WebsiteConfiguration:
        IndexDocument: index.html
        ErrorDocument: index.html
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false

  FrontendBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref FrontendBucket
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal: '*'
            Action: 's3:GetObject'
            Resource: !Sub '${FrontendBucket}/*'

Outputs:
  ApiUrl:
    Description: "API Gateway URL"
    Value: !Sub "https://${TradingApi}.execute-api.${AWS::Region}.amazonaws.com/prod"
  
  FrontendBucket:
    Description: "Frontend S3 bucket"
    Value: !Ref FrontendBucket
  
  FrontendUrl:
    Description: "Frontend URL"
    Value: !Sub "http://${FrontendBucket}.s3-website-${AWS::Region}.amazonaws.com"
