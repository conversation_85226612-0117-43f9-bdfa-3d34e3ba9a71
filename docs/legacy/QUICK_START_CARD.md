# 🚀 Quick Start Card - Bitcoin Trading Simulator

## **🎯 3-Step Process**
1. **Open**: http://localhost:3000
2. **Choose Strategy**: Click "Strategy" tab → Select "Balanced Ensemble (CHAMPION)"
3. **Run Simulation**: Click "Simulation" tab → Click "Create & Start Simulation"

---

## **📊 Strategy Quick Guide**

| Strategy | Risk Level | Best For | When to Use |
|----------|------------|----------|-------------|
| 🏆 **Balanced Ensemble** | Medium | **RECOMMENDED** | All market conditions |
| 🛡️ **Conservative** | Low | Capital preservation | Bear markets, risk-averse |
| ⚡ **Aggressive** | High | Higher returns | Bull markets, growth focus |
| 📈 **MA Crossover** | Medium | Trend following | Trending markets |
| 🔄 **RSI Mean Reversion** | Medium | Contrarian plays | Sideways markets |
| 🚀 **Momentum** | High | Pure momentum | Strong trends |

---

## **📈 Key Metrics to Watch**

| Metric | Good Result | What It Means |
|--------|-------------|---------------|
| **Total Return** | Positive, beats Bitcoin | Your profit/loss |
| **Sharpe Ratio** | > 1.0 (excellent: > 2.0) | Risk-adjusted returns |
| **Max Drawdown** | < -20% | Worst losing streak |
| **Win Rate** | > 50% | % of profitable trades |
| **Profit Factor** | > 1.5 | Profits ÷ Losses |

---

## **⚙️ Recommended Settings**

**For Conservative Approach:**
- Initial Capital: $100,000
- Position Size: 10-15%
- Strategy: Conservative Ensemble

**For Balanced Approach:**
- Initial Capital: $100,000
- Position Size: 20%
- Strategy: Balanced Ensemble ⭐

**For Aggressive Approach:**
- Initial Capital: $100,000
- Position Size: 25-30%
- Strategy: Aggressive Ensemble

---

## **🚨 Red Flags**
- ❌ Sharpe Ratio < 0.5
- ❌ Max Drawdown > -30%
- ❌ Extremely volatile returns
- ❌ Only works in specific conditions

---

## **💡 Pro Tips**
- ✅ Start with Balanced Ensemble
- ✅ Test different time periods
- ✅ Compare to buy-and-hold Bitcoin
- ✅ Consider transaction costs
- ✅ Match strategy to risk tolerance

---

## **🎮 Navigation**
- **Dashboard**: View results and performance
- **Strategy**: Choose and configure trading approach
- **Simulation**: Set investment parameters and run tests

---

**🌐 Access: http://localhost:3000**
