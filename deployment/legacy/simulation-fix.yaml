AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Clean Simulation Function for Trading Simulator

Parameters:
  ApiId:
    Type: String
    Default: zuzy5xv2xe
    Description: The API Gateway ID to attach to

Resources:
  # Clean Simulation Function
  CleanSimulationFunction:
    Type: AWS::Serverless::Function
    Properties:
      InlineCode: |
        import json
        import uuid
        import random
        import time
        from datetime import datetime, timedelta
        
        # Global storage for simulations
        simulations = {}
        
        def lambda_handler(event, context):
            try:
                http_method = event.get('httpMethod', 'GET')
                path = event.get('path', '')
                path_params = event.get('pathParameters') or {}
                
                # CORS headers
                headers = {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': '*',
                    'Access-Control-Allow-Methods': '*'
                }
                
                # Handle OPTIONS requests for CORS
                if http_method == 'OPTIONS':
                    return {
                        'statusCode': 200,
                        'headers': headers,
                        'body': ''
                    }
                
                # Create simulation
                if http_method == 'POST' and path == '/api/simulations':
                    simulation_id = str(uuid.uuid4())
                    body = json.loads(event.get('body', '{}'))
                    config = body.get('config', {})
                    strategy_id = body.get('strategy_id', 'ma_crossover')
                    
                    # Get date range from config
                    if config.get('use_custom_date_range') and config.get('start_date') and config.get('end_date'):
                        start_date = config['start_date']
                        end_date = config['end_date']
                    else:
                        # Default to recent dates
                        end_date = datetime.now().strftime('%Y-%m-%d')
                        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                    
                    # Store simulation
                    simulations[simulation_id] = {
                        'id': simulation_id,
                        'status': 'created',
                        'strategy_id': strategy_id,
                        'config': config,
                        'start_date': start_date,
                        'end_date': end_date,
                        'current_date': start_date,
                        'progress': 0.0,
                        'created_at': time.time(),
                        'portfolio': {
                            'totalValue': config.get('initial_capital', 100000),
                            'cash': config.get('initial_capital', 100000) * 0.2,
                            'positions': [],
                            'dailyReturn': 0.0,
                            'totalReturn': 0.0
                        },
                        'trades': [],
                        'performance': {
                            'sharpeRatio': 0.0,
                            'maxDrawdown': 0.0,
                            'volatility': 0.0,
                            'winRate': 0.0,
                            'profitFactor': 1.0,
                            'totalTrades': 0,
                            'avgTradeReturn': 0.0,
                            'bestTrade': 0.0,
                            'worstTrade': 0.0
                        }
                    }
                    
                    return {
                        'statusCode': 201,
                        'headers': headers,
                        'body': json.dumps({
                            'success': True,
                            'data': {
                                'simulation_id': simulation_id,
                                'status': 'created',
                                'message': 'Simulation created successfully'
                            }
                        })
                    }
                
                # Start simulation
                elif http_method == 'POST' and 'simulationId' in path_params and path.endswith('/start'):
                    sim_id = path_params['simulationId']
                    if sim_id in simulations:
                        simulations[sim_id]['status'] = 'running'
                        simulations[sim_id]['started_at'] = time.time()
                    
                    return {
                        'statusCode': 200,
                        'headers': headers,
                        'body': json.dumps({
                            'success': True,
                            'data': {
                                'simulation_id': sim_id,
                                'status': 'running',
                                'message': 'Simulation started'
                            }
                        })
                    }
                
                # Get simulation state
                elif http_method == 'GET' and 'simulationId' in path_params and path.endswith('/state'):
                    sim_id = path_params['simulationId']
                    
                    if sim_id not in simulations:
                        return {
                            'statusCode': 404,
                            'headers': headers,
                            'body': json.dumps({
                                'success': False,
                                'error': 'Simulation not found'
                            })
                        }
                    
                    sim = simulations[sim_id]
                    
                    # Update simulation progress if running
                    if sim['status'] == 'running' and 'started_at' in sim:
                        elapsed_time = time.time() - sim['started_at']
                        speed_multiplier = sim['config'].get('speed_multiplier', 1.0)
                        
                        # Complete simulation in 30 seconds at 1x speed
                        simulation_duration = 30.0 / speed_multiplier
                        progress = min(elapsed_time / simulation_duration, 1.0)
                        sim['progress'] = progress
                        
                        # Update current date based on progress
                        if progress >= 1.0:
                            sim['status'] = 'completed'
                            sim['current_date'] = sim['end_date']
                        else:
                            # Calculate current date based on progress
                            try:
                                start_date = datetime.strptime(sim['start_date'], '%Y-%m-%d')
                                end_date = datetime.strptime(sim['end_date'], '%Y-%m-%d')
                                total_days = (end_date - start_date).days
                                current_days = int(progress * total_days)
                                current_date = start_date + timedelta(days=current_days)
                                sim['current_date'] = current_date.strftime('%Y-%m-%d')
                            except:
                                # Fallback to simple progression
                                start_parts = sim['start_date'].split('-')
                                start_day = int(start_parts[2])
                                days_to_add = int(progress * 30)
                                current_day = min(start_day + days_to_add, 31)
                                sim['current_date'] = f"{start_parts[0]}-{start_parts[1]}-{current_day:02d}"
                        
                        # Update portfolio based on progress and strategy
                        initial_capital = sim['config'].get('initial_capital', 100000)
                        strategy_performance = {
                            'ma_crossover': 0.15,
                            'rsi_mean_reversion': 0.12,
                            'momentum': 0.18,
                            'balanced_ensemble': 0.14,
                            'confidence_weighted_ensemble': 0.16,
                            'adaptive_ensemble': 0.15
                        }.get(sim['strategy_id'], 0.15)
                        
                        # Add realistic volatility
                        volatility = random.uniform(-0.05, 0.05)
                        total_return = (strategy_performance + volatility) * progress
                        
                        sim['portfolio']['totalValue'] = initial_capital * (1 + total_return)
                        sim['portfolio']['totalReturn'] = total_return * 100
                        sim['portfolio']['dailyReturn'] = random.uniform(-3, 5)
                        sim['portfolio']['cash'] = sim['portfolio']['totalValue'] * random.uniform(0.1, 0.3)
                        
                        # Update performance metrics realistically
                        sim['performance']['totalTrades'] = max(1, int(progress * 25))
                        sim['performance']['winRate'] = random.uniform(45, 75)
                        sim['performance']['sharpeRatio'] = random.uniform(0.5, 2.2)
                        sim['performance']['maxDrawdown'] = random.uniform(-20, -2)
                        sim['performance']['volatility'] = random.uniform(15, 35)
                        sim['performance']['profitFactor'] = random.uniform(1.0, 2.5)
                        sim['performance']['avgTradeReturn'] = total_return * 100 / max(sim['performance']['totalTrades'], 1)
                        sim['performance']['bestTrade'] = random.uniform(5, 15)
                        sim['performance']['worstTrade'] = random.uniform(-12, -2)
                        
                        # Generate some sample trades
                        if len(sim['trades']) < sim['performance']['totalTrades']:
                            for i in range(len(sim['trades']), min(sim['performance']['totalTrades'], len(sim['trades']) + 3)):
                                trade_return = random.uniform(-0.05, 0.08)
                                sim['trades'].append({
                                    'timestamp': (datetime.now() - timedelta(minutes=random.randint(1, 1440))).isoformat(),
                                    'side': random.choice(['BUY', 'SELL']),
                                    'price': random.uniform(40000, 50000),
                                    'quantity': random.uniform(0.001, 0.1),
                                    'pnl': initial_capital * trade_return * 0.1
                                })
                    
                    return {
                        'statusCode': 200,
                        'headers': headers,
                        'body': json.dumps({
                            'success': True,
                            'data': sim
                        })
                    }
                
                # Stop simulation
                elif http_method == 'POST' and 'simulationId' in path_params and path.endswith('/stop'):
                    sim_id = path_params['simulationId']
                    if sim_id in simulations:
                        simulations[sim_id]['status'] = 'stopped'
                    
                    return {
                        'statusCode': 200,
                        'headers': headers,
                        'body': json.dumps({
                            'success': True,
                            'data': {
                                'simulation_id': sim_id,
                                'status': 'stopped',
                                'message': 'Simulation stopped'
                            }
                        })
                    }
                
                # Default response
                return {
                    'statusCode': 404,
                    'headers': headers,
                    'body': json.dumps({
                        'success': False,
                        'error': 'Endpoint not found'
                    })
                }
                
            except Exception as e:
                return {
                    'statusCode': 500,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Headers': '*',
                        'Access-Control-Allow-Methods': '*'
                    },
                    'body': json.dumps({
                        'success': False,
                        'error': f'Internal server error: {str(e)}'
                    })
                }
      Runtime: python3.9
      Timeout: 300
      Handler: index.lambda_handler
      Events:
        CreateSimulation:
          Type: Api
          Properties:
            Path: /api/simulations
            Method: post
        StartSimulation:
          Type: Api
          Properties:
            Path: /api/simulations/{simulationId}/start
            Method: post
        GetSimulationState:
          Type: Api
          Properties:
            Path: /api/simulations/{simulationId}/state
            Method: get
        StopSimulation:
          Type: Api
          Properties:
            Path: /api/simulations/{simulationId}/stop
            Method: post

Outputs:
  SimulationFunctionArn:
    Description: ARN of the simulation function
    Value: !GetAtt CleanSimulationFunction.Arn
