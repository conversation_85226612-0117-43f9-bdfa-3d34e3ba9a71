import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiService, queryKeys } from '../services/api';
import { StrategyParams, StrategyOption } from '../types';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Select } from '../components/ui/Select';
import { Input, Slider } from '../components/ui/Input';
import { InfoTooltip } from '../components/ui/Tooltip';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface StrategyConfigurationProps {
  onNext: (strategyId: string, parameters: StrategyParams) => void;
}

export const StrategyConfiguration: React.FC<StrategyConfigurationProps> = ({ onNext }) => {
  const [selectedStrategy, setSelectedStrategy] = useState<string>('');
  const [parameters, setParameters] = useState<any>({});

  // Load strategies
  const { data: strategiesResponse, isLoading } = useQuery({
    queryKey: queryKeys.strategies,
    queryFn: () => apiService.getAvailableStrategies(),
  });

  const strategies = strategiesResponse?.data || [];

  // Set initial strategy
  useEffect(() => {
    if (strategies.length > 0 && !selectedStrategy) {
      setSelectedStrategy(strategies[0].id);
    }
  }, [strategies, selectedStrategy]);

  // Load default parameters when strategy changes
  useEffect(() => {
    if (selectedStrategy) {
      // Set default parameters based on strategy
      const defaultParams = getDefaultParameters(selectedStrategy);
      setParameters(defaultParams);
    }
  }, [selectedStrategy]);

  const getDefaultParameters = (strategyId: string) => {
    switch (strategyId) {
      case 'ma_crossover':
        return {
          fast_period: 10,
          slow_period: 20,
          ma_type: 'sma',
          min_crossover_strength: 0.02
        };
      case 'rsi_mean_reversion':
        return {
          rsi_period: 14,
          oversold_threshold: 30,
          overbought_threshold: 70
        };
      case 'momentum':
        return {
          lookback_period: 20,
          momentum_threshold: 0.02
        };
      case 'balanced_ensemble':
      case 'confidence_weighted_ensemble':
      case 'adaptive_ensemble':
        return {
          ensemble_method: 'confidence_weighted',
          min_consensus: 0.5,
          confidence_threshold: 0.3,
          strategy_weights: {
            ma_crossover: 0.33,
            rsi_mean_reversion: 0.33,
            momentum: 0.34
          }
        };
      default:
        return {};
    }
  };

  const selectedStrategyData = strategies.find((s: StrategyOption) => s.id === selectedStrategy);

  const handleNext = () => {
    if (!selectedStrategy) {
      toast.error('Please select a strategy');
      return;
    }
    if (!parameters || Object.keys(parameters).length === 0) {
      toast.error('Please configure strategy parameters');
      return;
    }
    onNext(selectedStrategy, parameters);
  };

  const renderParameterInputs = () => {
    if (!selectedStrategy) return null;

    switch (selectedStrategy) {
      case 'ma_crossover':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Fast Period"
              type="number"
              value={parameters.fast_period || 10}
              onChange={(e) => setParameters({...parameters, fast_period: Number(e.target.value)})}
              min={5}
              max={50}
              fullWidth
            />
            <Input
              label="Slow Period"
              type="number"
              value={parameters.slow_period || 20}
              onChange={(e) => setParameters({...parameters, slow_period: Number(e.target.value)})}
              min={20}
              max={200}
              fullWidth
            />
            <Select
              label="MA Type"
              value={parameters.ma_type || 'sma'}
              onChange={(value) => setParameters({...parameters, ma_type: value})}
              options={[
                { value: 'sma', label: 'Simple MA' },
                { value: 'ema', label: 'Exponential MA' }
              ]}
              fullWidth
            />
            <Slider
              label="Min Crossover Strength"
              value={parameters.min_crossover_strength || 0.02}
              onChange={(value) => setParameters({...parameters, min_crossover_strength: value})}
              min={0}
              max={0.1}
              step={0.01}
              formatValue={(v) => `${(v * 100).toFixed(1)}%`}
            />
          </div>
        );

      case 'rsi_mean_reversion':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="RSI Period"
              type="number"
              value={parameters.rsi_period || 14}
              onChange={(e) => setParameters({...parameters, rsi_period: Number(e.target.value)})}
              min={5}
              max={30}
              fullWidth
            />
            <div className="space-y-4">
              <Slider
                label="Oversold Threshold"
                value={parameters.oversold_threshold || 30}
                onChange={(value) => setParameters({...parameters, oversold_threshold: value})}
                min={10}
                max={40}
                step={1}
                formatValue={(v) => v.toString()}
              />
              <Slider
                label="Overbought Threshold"
                value={parameters.overbought_threshold || 70}
                onChange={(value) => setParameters({...parameters, overbought_threshold: value})}
                min={60}
                max={90}
                step={1}
                formatValue={(v) => v.toString()}
              />
            </div>
          </div>
        );

      case 'momentum':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Lookback Period"
              type="number"
              value={parameters.lookback_period || 20}
              onChange={(e) => setParameters({...parameters, lookback_period: Number(e.target.value)})}
              min={5}
              max={100}
              fullWidth
            />
            <Slider
              label="Momentum Threshold"
              value={parameters.momentum_threshold || 0.02}
              onChange={(value) => setParameters({...parameters, momentum_threshold: value})}
              min={0.001}
              max={0.1}
              step={0.001}
              formatValue={(v) => `${(v * 100).toFixed(1)}%`}
            />
          </div>
        );

      case 'balanced_ensemble':
      case 'confidence_weighted_ensemble':
      case 'adaptive_ensemble':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Ensemble Method"
                value={parameters.ensemble_method || 'confidence_weighted'}
                onChange={(value) => setParameters({...parameters, ensemble_method: value})}
                options={[
                  { value: 'confidence_weighted', label: 'Confidence Weighted' },
                  { value: 'weighted_voting', label: 'Weighted Voting' },
                  { value: 'majority_voting', label: 'Majority Voting' }
                ]}
                fullWidth
              />
              <Slider
                label="Min Consensus"
                value={parameters.min_consensus || 0.5}
                onChange={(value) => setParameters({...parameters, min_consensus: value})}
                min={0.1}
                max={0.9}
                step={0.1}
                formatValue={(v) => `${(v * 100).toFixed(0)}%`}
              />
            </div>
            <Slider
              label="Confidence Threshold"
              value={parameters.confidence_threshold || 0.3}
              onChange={(value) => setParameters({...parameters, confidence_threshold: value})}
              min={0.1}
              max={0.9}
              step={0.1}
              formatValue={(v) => `${(v * 100).toFixed(0)}%`}
            />
          </div>
        );

      default:
        return <div className="text-gray-500">No parameters available for this strategy.</div>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Strategy Configuration</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Choose your trading strategy and configure its parameters
        </p>
        <div className="flex justify-center mt-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
            <div className="w-16 h-1 bg-gray-300 dark:bg-gray-600"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">2</div>
            <div className="w-16 h-1 bg-gray-300 dark:bg-gray-600"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
            <div className="w-16 h-1 bg-gray-300 dark:bg-gray-600"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">4</div>
          </div>
        </div>
      </div>

      {/* Strategy Selection */}
      <Card title="Select Trading Strategy" loading={isLoading}>
        <div className="space-y-4">
          <Select
            label="Trading Strategy"
            value={selectedStrategy}
            onChange={setSelectedStrategy}
            options={strategies.map((strategy: StrategyOption) => ({
              value: strategy.id,
              label: strategy.name,
              description: strategy.description
            }))}
            placeholder="Select a strategy..."
            fullWidth
          />

          {selectedStrategyData && (
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {selectedStrategyData.name}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {selectedStrategyData.description}
                  </p>
                </div>
                <InfoTooltip
                  title={selectedStrategyData.name}
                  description={selectedStrategyData.description}
                />
              </div>

              <div className="mt-3 flex space-x-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                  {selectedStrategyData.category}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  Risk: {selectedStrategyData.risk_level}
                </span>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Strategy Parameters */}
      {selectedStrategy && (
        <Card title="Configure Parameters" subtitle="Customize your strategy settings">
          {renderParameterInputs()}
        </Card>
      )}

      {/* Next Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleNext}
          icon={<ArrowRightIcon />}
          disabled={!selectedStrategy || Object.keys(parameters).length === 0}
          size="lg"
        >
          Next: Simulation Setup
        </Button>
      </div>
    </div>
  );
};
