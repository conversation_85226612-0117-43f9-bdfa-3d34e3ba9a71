# Trading Simulator v1.0 - Deployment Guide

Complete deployment instructions for the Real-Data Simulation System.

## ✅ **Current Production System**
- **Live Frontend:** http://trading-frontend-************-**********.s3-website-us-east-1.amazonaws.com
- **Live API:** https://zuzy5xv2xe.execute-api.us-east-1.amazonaws.com/prod
- **Status:** ✅ Fully operational with real market data
- **Version:** v1.0-real-data-simulation (Milestone complete)

## 🎯 **Prerequisites**

### **AWS Requirements:**
- AWS CLI configured with appropriate permissions
- AWS account with CloudFormation, Lambda, and API Gateway access
- Region: us-east-1 (recommended for optimal performance)

### **Required Permissions:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "cloudformation:*",
        "lambda:*",
        "apigateway:*",
        "iam:CreateRole",
        "iam:AttachRolePolicy",
        "iam:PassRole"
      ],
      "Resource": "*"
    }
  ]
}
```

## 🚀 **Quick Deployment**

### **Option 1: One-Command Deployment**
```bash
# Deploy the complete system
aws cloudformation deploy \
  --template-file deployment/cloudformation/main-template.yaml \
  --stack-name trading-simulator-v1 \
  --capabilities CAPABILITY_IAM \
  --region us-east-1 \
  --no-fail-on-empty-changeset
```

### **Option 2: Step-by-Step Deployment**

#### **1. Validate Template:**
```bash
aws cloudformation validate-template \
  --template-body file://deployment/cloudformation/main-template.yaml
```

#### **2. Deploy Stack:**
```bash
aws cloudformation create-stack \
  --stack-name trading-simulator-v1 \
  --template-body file://deployment/cloudformation/main-template.yaml \
  --capabilities CAPABILITY_IAM \
  --region us-east-1
```

#### **3. Monitor Deployment:**
```bash
aws cloudformation describe-stacks \
  --stack-name trading-simulator-v1 \
  --region us-east-1
```

## 🔍 **Deployment Verification**

### **1. Check Stack Status:**
```bash
aws cloudformation describe-stacks \
  --stack-name trading-simulator-v1 \
  --query 'Stacks[0].StackStatus'
```

### **2. Get API Gateway URL:**
```bash
aws cloudformation describe-stacks \
  --stack-name trading-simulator-v1 \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiUrl`].OutputValue' \
  --output text
```

### **3. Test Health Endpoint:**
```bash
# Replace with your actual API URL
curl "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/health"
```

Expected response:
```json
{
  "success": true,
  "message": "Trading Simulator API is healthy",
  "timestamp": "2025-01-15T10:00:00Z",
  "version": "1.0.0"
}
```

## 🧪 **Testing Deployment**

### **1. Test All Endpoints:**
```bash
# Health check
curl "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/health"

# Available strategies
curl "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/strategies"

# Market data
curl "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/market-data/BTC-USD?days=30"

# Custom date range
curl "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/market-data/BTC-USD?start_date=2023-01-01&end_date=2023-01-31"
```

### **2. Test Simulation Creation:**
```bash
curl -X POST "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/simulations" \
  -H "Content-Type: application/json" \
  -d '{
    "strategy_id": "ma_crossover",
    "strategy_params": {
      "fast_period": 10,
      "slow_period": 20
    },
    "config": {
      "initial_capital": 100000,
      "position_size": 0.4,
      "use_custom_date_range": true,
      "start_date": "2023-01-01",
      "end_date": "2023-01-31"
    }
  }'
```

### **3. Test Simulation Execution:**
```bash
# Start simulation (replace SIMULATION_ID with actual ID from creation response)
curl -X POST "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/simulations/SIMULATION_ID/start"

# Check simulation state
curl "https://YOUR-API-ID.execute-api.us-east-1.amazonaws.com/prod/api/simulations/SIMULATION_ID/state"
```

## 🔧 **Configuration Options**

### **Environment Variables:**
The CloudFormation template supports the following customizations:

```yaml
Parameters:
  Environment:
    Type: String
    Default: prod
    AllowedValues: [dev, staging, prod]
  
  LogLevel:
    Type: String
    Default: INFO
    AllowedValues: [DEBUG, INFO, WARN, ERROR]
```

### **Custom Deployment:**
```bash
aws cloudformation deploy \
  --template-file deployment/cloudformation/main-template.yaml \
  --stack-name trading-simulator-v1 \
  --capabilities CAPABILITY_IAM \
  --parameter-overrides \
    Environment=staging \
    LogLevel=DEBUG
```

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **1. Permission Denied:**
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify IAM permissions
aws iam simulate-principal-policy \
  --policy-source-arn arn:aws:iam::ACCOUNT:user/USERNAME \
  --action-names cloudformation:CreateStack \
  --resource-arns "*"
```

#### **2. Template Validation Errors:**
```bash
# Validate template syntax
aws cloudformation validate-template \
  --template-body file://deployment/cloudformation/main-template.yaml
```

#### **3. Stack Creation Failed:**
```bash
# Check stack events
aws cloudformation describe-stack-events \
  --stack-name trading-simulator-v1
```

#### **4. Lambda Function Errors:**
```bash
# Check Lambda logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/trading"

# View recent logs
aws logs filter-log-events \
  --log-group-name "/aws/lambda/trading-simulator-v1-HealthFunction" \
  --start-time $(date -d '1 hour ago' +%s)000
```

## 🔄 **Updates and Maintenance**

### **Update Deployment:**
```bash
# Update existing stack
aws cloudformation deploy \
  --template-file deployment/cloudformation/main-template.yaml \
  --stack-name trading-simulator-v1 \
  --capabilities CAPABILITY_IAM \
  --region us-east-1
```

### **Rollback Deployment:**
```bash
# Cancel update and rollback
aws cloudformation cancel-update-stack \
  --stack-name trading-simulator-v1

# Or rollback to previous version
aws cloudformation continue-update-rollback \
  --stack-name trading-simulator-v1
```

### **Delete Stack:**
```bash
# Delete entire stack (WARNING: This removes all resources)
aws cloudformation delete-stack \
  --stack-name trading-simulator-v1
```

## 📊 **Monitoring**

### **CloudWatch Metrics:**
- Lambda function invocations and errors
- API Gateway request counts and latencies
- Custom application metrics

### **Logging:**
- All Lambda functions log to CloudWatch
- API Gateway access logs available
- Custom application logging with structured format

### **Alerts:**
Set up CloudWatch alarms for:
- High error rates
- Increased latency
- Function timeouts
- API throttling

## 🔐 **Security Considerations**

### **API Security:**
- CORS enabled for frontend integration
- Rate limiting via API Gateway
- Input validation in Lambda functions

### **IAM Roles:**
- Least privilege principle
- Function-specific execution roles
- No hardcoded credentials

### **Data Protection:**
- No sensitive data stored in logs
- Encrypted data in transit
- Secure API endpoints

## 📈 **Performance Optimization**

### **Lambda Configuration:**
- Memory: 512MB (adjustable based on usage)
- Timeout: 300 seconds for simulation functions
- Concurrent executions: 1000 (default)

### **API Gateway:**
- Caching enabled for market data endpoints
- Compression enabled for responses
- Request/response transformation optimized

## 🎯 **Production Readiness**

### **Checklist:**
- [ ] All endpoints tested and functional
- [ ] Error handling implemented
- [ ] Logging configured
- [ ] Monitoring set up
- [ ] Security review completed
- [ ] Performance testing done
- [ ] Documentation updated
- [ ] Backup strategy defined

---

**Trading Simulator v1.0 Deployment Guide** - Professional deployment for real-data simulation system 🚀📊
