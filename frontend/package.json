{"name": "trading-simulator-frontend", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.17", "@tanstack/react-query": "^4.28.0", "chart.js": "^4.2.1", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^1.2.1", "date-fns": "^2.29.3", "framer-motion": "^10.12.4", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.9", "react-hot-toast": "^2.4.0", "react-scripts": "5.0.1", "recharts": "^2.5.0", "tailwindcss": "^3.3.0", "web-vitals": "^3.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "typescript": "^4.9.5"}, "proxy": "http://localhost:8000"}